import * as React from 'react';
import { CloseIcon } from '@fluentui/react-icons-northstar';
import useIndexedDbAccessor from '../../../../hooks/accessors/useIndexedDbAccessor';
import useTeamsChatsRepositoryAccessor from '../../../../hooks/accessors/useTeamsChatsRepositoryAccessor';
import { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';

// CSS
import './SelectedItemsList.scss';

export interface ISelectedItemsListProps {
  selectedItems: Set<string>;
  onRemoveItem: (id: string) => void;
  allChatItems: IUserChatItem[];
}

/**
 * IUserChatItemとITeamsChatsItem
 * 両方の型のプロパティを含む統合型
 */
export interface ISelectedItem {
  // 共通プロパティ
  id: string; // chatId または channelId
  name: string;
  type: 'チャット' | 'チャネル';
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'TeamsChannel';
  teamId?: string;
  // ITeamsChatsItemのみが持つプロパティ（オプショナル）
  countId?: number; // 選択順序
}

/**
 * SelectedItemsList
 * 選択されたチャット・チャネルアイテムを表示するコンポーネント
 */
const SelectedItemsList: React.FC<ISelectedItemsListProps> = (props) => {
  const {
    selectedItems,
    onRemoveItem,
    allChatItems = [],
  } = props;

  // IndexedDBアクセサーを初期化
  const [openDB] = useIndexedDbAccessor();
  const { allTeamsChats } = useTeamsChatsRepositoryAccessor(openDB);

  // 選択されたアイテムの詳細情報を取得し表示できる形に変更（新しいアイテムを左側に表示するため逆順）
  const selectedItemDetails = React.useMemo(() => Array.from(selectedItems)
    .map((id): ISelectedItem => {
      // IndexedDBから詳細情報を取得
      const foundInIndexedDB = allTeamsChats.find((item) => item.id === id);
      if (foundInIndexedDB) {
        return foundInIndexedDB;
      }

      // IndexedDBに見つからない場合は、allChatItemsから取得
      const foundInAllChatItems = allChatItems.find((item) => item.id === id);
      if (foundInAllChatItems) {
        return foundInAllChatItems;
      }

      // どちらにも見つからない場合は、最低限の情報で表示用アイテムを作成(使用しないことが前提)
      return {
        id,
        name: `選択済みアイテム (${id.substring(0, 8)}...)`,
        type: 'チャット' as const,
        chatType: 'oneOnOne' as const,
        countId: 0,
      };
    })
    .reverse(), [selectedItems, allTeamsChats, allChatItems]);

  // 選択されたアイテムがない場合は何も表示しない
  if (selectedItemDetails.length === 0) {
    return null;
  }

  return (
    <div className="selected-items-list">
      <div className="selected-items-container">
        {selectedItemDetails.map((item) => (
          <div key={item.id} className="selected-item">
            <span className="selected-item-type">{item.type}</span>
            <span className="selected-item-name">{item.name}</span>
            <button
              type="button"
              className="selected-item-remove"
              onClick={() => onRemoveItem(item.id)}
              aria-label={`${item.name}の選択を解除`}
              title={`${item.name}の選択を解除`}
            >
              <CloseIcon />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SelectedItemsList;
